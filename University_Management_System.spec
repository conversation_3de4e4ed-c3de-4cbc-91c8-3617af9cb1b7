# -*- mode: python ; coding: utf-8 -*-


a = Analysis(
    ['run.py'],
    pathex=[],
    binaries=[],
    datas=[('university_logo.ico', '.'), ('university_logo.png', '.'), ('university_management_system', 'university_management_system')],
    hiddenimports=['tkinter', 'tkinter.ttk', 'PIL', 'PIL.Image', 'PIL.ImageTk', 'reportlab', 'reportlab.pdfgen', 'reportlab.lib', 'ttkbootstrap', 'tkcalendar', 'sqlite3'],
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=[],
    noarchive=False,
    optimize=0,
)
pyz = PYZ(a.pure)

exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.datas,
    [],
    name='University_Management_System',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    upx_exclude=[],
    runtime_tmpdir=None,
    console=False,
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
    icon=['university_logo.ico'],
)
