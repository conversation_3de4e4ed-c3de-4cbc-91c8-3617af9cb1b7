# University Management System

## Installation Instructions

1. Run University_Management_System.exe to start the application
2. The application will create a database automatically on first run
3. Use the following default credentials to login:

### Admin Login:
- Username: admin
- Password: admin123

### Professor Login:
- Username: faisalhafeez
- Password: faisalhafeez

### Student Login:
- Username: john
- Password: john

## System Requirements

- Windows 7 or later
- No additional software required (standalone executable)

## Features

- Admin Panel: Manage students, professors, courses, and semesters
- Professor <PERSON>: Take attendance, manage grades, view courses
- Student Portal: View results, attendance, and generate reports
- PDF Report Generation
- Multi-semester support

## Support

For technical support, please contact the system administrator.

## Version

University Management System v1.0
Built with Python and Tkinter
