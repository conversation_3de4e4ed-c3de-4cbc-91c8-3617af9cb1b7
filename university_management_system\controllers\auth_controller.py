from ..models.user import User
from ..models.student import Student
from ..models.professor import Professor
from ..models.admin import Admin

class AuthController:
    @staticmethod
    def login(username, password):
        """
        Authenticate a user and return user details based on role
        """
        user = User.authenticate(username, password)

        if not user:
            return None, "Invalid username or password"

        if user.role == 'admin':
            admin = Admin.get_by_user_id(user.id)
            if not admin:
                return None, f"Admin profile not found for user '{user.username}'. Please contact system administrator."
            return {'user': user, 'profile': admin, 'role': 'admin'}, "Login successful"

        elif user.role == 'professor':
            professor = Professor.get_by_user_id(user.id)
            if not professor:
                return None, f"Professor profile not found for user '{user.username}'. Please contact system administrator."
            return {'user': user, 'profile': professor, 'role': 'professor'}, "Login successful"

        elif user.role == 'student':
            student = Student.get_by_user_id(user.id)
            if not student:
                return None, f"Student profile not found for user '{user.username}'. Please contact system administrator."
            return {'user': user, 'profile': student, 'role': 'student'}, "Login successful"

        return None, "Invalid user role"

    @staticmethod
    def change_password(user_id, current_password, new_password):
        """
        Change user password
        """
        try:
            # Get user from database
            user = User.get_by_id(user_id)

            if not user:
                return False, "User not found"

            # Verify current password
            if not user.verify_password(current_password):
                return False, "Current password is incorrect"

            # Update password
            user.set_password(new_password)
            user.save()

            return True, "Password changed successfully"

        except Exception as e:
            print(f"Error changing password: {e}")
            return False, f"Error changing password: {e}"

    @staticmethod
    def reset_password(username):
        """
        Reset user password to default (username)
        """
        user = User.get_by_username(username)

        if not user:
            return False, "User not found"

        user.password = username  # Set password to username as default
        user.save()

        return True, "Password reset successfully"
