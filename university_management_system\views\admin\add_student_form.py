import tkinter as tk
from tkinter import ttk, messagebox
import datetime
from ...controllers.admin_controller import AdminController
from ...utils.validators import Validators
from ...database.db_config import get_db_connection, close_connection

class AddStudentForm:
    def __init__(self, parent):
        self.parent = parent
        
        # Create main frame
        self.main_frame = ttk.Frame(parent, padding=20)
        self.main_frame.pack(fill=tk.BOTH, expand=True)
        
        # Create form title
        title_label = ttk.Label(self.main_frame, text="Add New Student", font=("Arial", 16, "bold"))
        title_label.grid(row=0, column=0, columnspan=2, sticky="w", pady=(0, 20))
        
        # Create form fields
        # Student ID
        ttk.Label(self.main_frame, text="Student ID:").grid(row=1, column=0, sticky="w", pady=5)
        self.student_id_var = tk.StringVar()
        self.student_id_entry = ttk.Entry(self.main_frame, width=30, textvariable=self.student_id_var)
        self.student_id_entry.grid(row=1, column=1, sticky="w", pady=5)
        
        # First Name
        ttk.Label(self.main_frame, text="First Name:").grid(row=2, column=0, sticky="w", pady=5)
        self.first_name_var = tk.StringVar()
        self.first_name_entry = ttk.Entry(self.main_frame, width=30, textvariable=self.first_name_var)
        self.first_name_entry.grid(row=2, column=1, sticky="w", pady=5)
        
        # Last Name
        ttk.Label(self.main_frame, text="Last Name:").grid(row=3, column=0, sticky="w", pady=5)
        self.last_name_var = tk.StringVar()
        self.last_name_entry = ttk.Entry(self.main_frame, width=30, textvariable=self.last_name_var)
        self.last_name_entry.grid(row=3, column=1, sticky="w", pady=5)
        
        # Email
        ttk.Label(self.main_frame, text="Email:").grid(row=4, column=0, sticky="w", pady=5)
        self.email_var = tk.StringVar()
        self.email_entry = ttk.Entry(self.main_frame, width=30, textvariable=self.email_var)
        self.email_entry.grid(row=4, column=1, sticky="w", pady=5)
        
        # Phone
        ttk.Label(self.main_frame, text="Phone:").grid(row=5, column=0, sticky="w", pady=5)
        self.phone_var = tk.StringVar()
        self.phone_entry = ttk.Entry(self.main_frame, width=30, textvariable=self.phone_var)
        self.phone_entry.grid(row=5, column=1, sticky="w", pady=5)
        
        # Department
        ttk.Label(self.main_frame, text="Department:").grid(row=6, column=0, sticky="w", pady=5)
        self.department_var = tk.StringVar()
        self.department_combobox = ttk.Combobox(self.main_frame, width=28, textvariable=self.department_var, state="readonly")
        self.department_combobox.grid(row=6, column=1, sticky="w", pady=5)
        
        # Semester
        ttk.Label(self.main_frame, text="Semester:").grid(row=7, column=0, sticky="w", pady=5)
        self.semester_var = tk.StringVar()
        self.semester_combobox = ttk.Combobox(self.main_frame, width=28, textvariable=self.semester_var, state="readonly")
        self.semester_combobox.grid(row=7, column=1, sticky="w", pady=5)
        self.semester_combobox['values'] = [str(i) for i in range(1, 9)]  # Semesters 1-8
        
        # Enrollment Date
        ttk.Label(self.main_frame, text="Enrollment Date:").grid(row=8, column=0, sticky="w", pady=5)
        self.enrollment_date_var = tk.StringVar()
        self.enrollment_date_var.set(datetime.date.today().strftime("%Y-%m-%d"))
        self.enrollment_date_entry = ttk.Entry(self.main_frame, width=30, textvariable=self.enrollment_date_var)
        self.enrollment_date_entry.grid(row=8, column=1, sticky="w", pady=5)
        
        # Username
        ttk.Label(self.main_frame, text="Username:").grid(row=9, column=0, sticky="w", pady=5)
        self.username_var = tk.StringVar()
        self.username_entry = ttk.Entry(self.main_frame, width=30, textvariable=self.username_var)
        self.username_entry.grid(row=9, column=1, sticky="w", pady=5)
        
        # Password
        ttk.Label(self.main_frame, text="Password:").grid(row=10, column=0, sticky="w", pady=5)
        self.password_var = tk.StringVar()
        self.password_entry = ttk.Entry(self.main_frame, width=30, textvariable=self.password_var, show="*")
        self.password_entry.grid(row=10, column=1, sticky="w", pady=5)

        # Password strength indicator
        self.password_strength_label = ttk.Label(self.main_frame, text="", font=("Arial", 8))
        self.password_strength_label.grid(row=10, column=2, sticky="w", padx=(10, 0), pady=5)

        # Bind password validation to real-time checking
        self.password_var.trace('w', self.validate_password_realtime)

        # Confirm Password
        ttk.Label(self.main_frame, text="Confirm Password:").grid(row=11, column=0, sticky="w", pady=5)
        self.confirm_password_var = tk.StringVar()
        self.confirm_password_entry = ttk.Entry(self.main_frame, width=30, textvariable=self.confirm_password_var, show="*")
        self.confirm_password_entry.grid(row=11, column=1, sticky="w", pady=5)

        # Password match indicator
        self.password_match_label = ttk.Label(self.main_frame, text="", font=("Arial", 8))
        self.password_match_label.grid(row=11, column=2, sticky="w", padx=(10, 0), pady=5)

        # Bind confirm password validation to real-time checking
        self.confirm_password_var.trace('w', self.validate_password_match_realtime)
        
        # Buttons frame
        buttons_frame = ttk.Frame(self.main_frame)
        buttons_frame.grid(row=12, column=0, columnspan=2, pady=20)
        
        # Save button
        save_button = ttk.Button(buttons_frame, text="Save", command=self.save_student)
        save_button.pack(side=tk.LEFT, padx=5)
        
        # Clear button
        clear_button = ttk.Button(buttons_frame, text="Clear", command=self.clear_form)
        clear_button.pack(side=tk.LEFT, padx=5)
        
        # Status label
        self.status_label = ttk.Label(self.main_frame, text="", foreground="red")
        self.status_label.grid(row=13, column=0, columnspan=2, pady=(10, 0))
        
        # Load departments
        self.load_departments()

    def validate_password_realtime(self, *args):
        """Real-time password validation as user types"""
        password = self.password_var.get()

        if not password:
            self.password_strength_label.config(text="", foreground="black")
            return

        if len(password) < 6:
            self.password_strength_label.config(
                text=f"⚠️ Too short ({len(password)}/6 min)",
                foreground="red"
            )
        elif len(password) < 8:
            self.password_strength_label.config(
                text="✓ Acceptable (6+ chars)",
                foreground="orange"
            )
        else:
            self.password_strength_label.config(
                text="✓ Strong (8+ chars)",
                foreground="green"
            )

        # Also check password match if confirm password is filled
        if self.confirm_password_var.get():
            self.validate_password_match_realtime()

    def validate_password_match_realtime(self, *args):
        """Real-time password match validation"""
        password = self.password_var.get()
        confirm_password = self.confirm_password_var.get()

        if not confirm_password:
            self.password_match_label.config(text="", foreground="black")
            return

        if password == confirm_password:
            self.password_match_label.config(
                text="✓ Passwords match",
                foreground="green"
            )
        else:
            self.password_match_label.config(
                text="✗ Passwords don't match",
                foreground="red"
            )

    def load_departments(self):
        """Load departments from database"""
        conn = get_db_connection()
        cursor = conn.cursor()
        
        cursor.execute("SELECT id, name FROM departments ORDER BY name")
        departments = cursor.fetchall()
        
        close_connection(conn)
        
        if departments:
            self.departments = {dept['name']: dept['id'] for dept in departments}
            self.department_combobox['values'] = list(self.departments.keys())
        else:
            self.departments = {}
            self.department_combobox['values'] = ["No departments available"]
    
    def validate_form(self):
        """Validate form inputs"""
        # Reset status label
        self.status_label.config(text="")
        
        # Validate Student ID
        valid, message = Validators.validate_required(self.student_id_var.get(), "Student ID")
        if not valid:
            self.status_label.config(text=message)
            return False
        
        # Validate First Name
        valid, message = Validators.validate_name(self.first_name_var.get(), "First Name")
        if not valid:
            self.status_label.config(text=message)
            return False
        
        # Validate Last Name
        valid, message = Validators.validate_name(self.last_name_var.get(), "Last Name")
        if not valid:
            self.status_label.config(text=message)
            return False
        
        # Validate Email
        valid, message = Validators.validate_email(self.email_var.get())
        if not valid:
            self.status_label.config(text=message)
            return False
        
        # Validate Phone
        valid, message = Validators.validate_phone(self.phone_var.get())
        if not valid:
            self.status_label.config(text=message)
            return False
        
        # Validate Department
        if not self.department_var.get() or self.department_var.get() not in self.departments:
            self.status_label.config(text="Please select a department")
            return False
        
        # Validate Semester
        valid, message = Validators.validate_required(self.semester_var.get(), "Semester")
        if not valid:
            self.status_label.config(text=message)
            return False
        
        # Validate Enrollment Date
        valid, message = Validators.validate_date(self.enrollment_date_var.get())
        if not valid:
            self.status_label.config(text=message)
            return False
        
        # Validate Username
        valid, message = Validators.validate_username(self.username_var.get())
        if not valid:
            self.status_label.config(text=message)
            return False
        
        # Validate Password
        valid, message = Validators.validate_password(self.password_var.get(), self.confirm_password_var.get())
        if not valid:
            self.status_label.config(text=message)
            return False
        
        return True
    
    def save_student(self):
        """Save student to database"""
        if not self.validate_form():
            return
        
        # Get form data
        student_id = self.student_id_var.get()
        first_name = self.first_name_var.get()
        last_name = self.last_name_var.get()
        email = self.email_var.get()
        phone = self.phone_var.get()
        department_id = self.departments[self.department_var.get()]
        semester = int(self.semester_var.get())
        enrollment_date = self.enrollment_date_var.get()
        username = self.username_var.get()
        password = self.password_var.get()
        
        # Create student
        success, message = AdminController.create_student(
            student_id, first_name, last_name, email, phone, department_id,
            semester, enrollment_date, username, password
        )
        
        if success:
            messagebox.showinfo("Success", "Student created successfully")
            self.clear_form()
        else:
            self.status_label.config(text=message)
    
    def clear_form(self):
        """Clear form fields"""
        self.student_id_var.set("")
        self.first_name_var.set("")
        self.last_name_var.set("")
        self.email_var.set("")
        self.phone_var.set("")
        self.department_var.set("")
        self.semester_var.set("")
        self.enrollment_date_var.set(datetime.date.today().strftime("%Y-%m-%d"))
        self.username_var.set("")
        self.password_var.set("")
        self.confirm_password_var.set("")
        self.status_label.config(text="")
