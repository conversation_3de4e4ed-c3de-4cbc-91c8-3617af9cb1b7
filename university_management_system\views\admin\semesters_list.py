import tkinter as tk
from tkinter import ttk, messagebox
from ...controllers.admin_controller import AdminController
from ...database.db_config import get_db_connection, close_connection
from ...utils.theme import ULTheme

class SemestersList:
    def __init__(self, parent):
        self.parent = parent
        
        # Create main frame
        self.main_frame = ttk.Frame(parent, padding=20)
        self.main_frame.pack(fill=tk.BOTH, expand=True)
        
        # Create title and action buttons
        self.create_header()
        
        # Create semesters table
        self.create_semesters_table()
        
        # Load semesters data
        self.load_semesters()
    
    def create_header(self):
        """Create header with title and action buttons"""
        # Header frame
        header_frame = ttk.Frame(self.main_frame)
        header_frame.pack(fill=tk.X, pady=(0, 20))
        
        # Title
        title_label = ttk.Label(header_frame, text="Semesters List", 
                               font=(ULTheme.FONT_FAMILY, ULTheme.FONT_SIZE_XLARGE, "bold"))
        title_label.pack(side=tk.LEFT)
        
        # Action buttons frame
        action_buttons_frame = ttk.Frame(header_frame)
        action_buttons_frame.pack(side=tk.RIGHT)
        
        # Add semester button
        add_button = ttk.Button(action_buttons_frame, text="Add Semester", 
                               command=self.on_add_semester)
        add_button.pack(side=tk.LEFT, padx=5)
        
        # Refresh button
        refresh_button = ttk.Button(action_buttons_frame, text="Refresh", 
                                   command=self.load_semesters)
        refresh_button.pack(side=tk.LEFT, padx=5)
    
    def create_semesters_table(self):
        """Create semesters table with scrollbar"""
        # Table frame with scrollbar
        table_frame = ttk.Frame(self.main_frame)
        table_frame.pack(fill=tk.BOTH, expand=True)
        
        # Scrollbar
        scrollbar = ttk.Scrollbar(table_frame)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        
        # Treeview for semesters table
        columns = ("id", "name", "start_date", "end_date", "is_active", "courses_count")
        self.semesters_table = ttk.Treeview(table_frame, columns=columns, show="headings",
                                          yscrollcommand=scrollbar.set)
        
        # Configure scrollbar
        scrollbar.config(command=self.semesters_table.yview)
        
        # Configure column headings
        self.semesters_table.heading("id", text="ID")
        self.semesters_table.heading("name", text="Semester Name")
        self.semesters_table.heading("start_date", text="Start Date")
        self.semesters_table.heading("end_date", text="End Date")
        self.semesters_table.heading("is_active", text="Active")
        self.semesters_table.heading("courses_count", text="Courses")
        
        # Configure column widths
        self.semesters_table.column("id", width=50, anchor=tk.CENTER)
        self.semesters_table.column("name", width=200)
        self.semesters_table.column("start_date", width=100, anchor=tk.CENTER)
        self.semesters_table.column("end_date", width=100, anchor=tk.CENTER)
        self.semesters_table.column("is_active", width=80, anchor=tk.CENTER)
        self.semesters_table.column("courses_count", width=80, anchor=tk.CENTER)
        
        # Pack table
        self.semesters_table.pack(fill=tk.BOTH, expand=True)
        
        # Bind double-click event for editing
        self.semesters_table.bind("<Double-1>", self.on_semester_double_click)
        
        # Add right-click context menu
        self.create_context_menu()
    
    def create_context_menu(self):
        """Create right-click context menu for semesters table"""
        self.context_menu = tk.Menu(self.semesters_table, tearoff=0)
        self.context_menu.add_command(label="Edit", command=self.on_edit_semester)
        self.context_menu.add_command(label="Delete", command=self.on_delete_semester)
        self.context_menu.add_separator()
        self.context_menu.add_command(label="Set as Active", command=self.on_set_active_semester)
        
        # Bind right-click event
        self.semesters_table.bind("<Button-3>", self.show_context_menu)
    
    def show_context_menu(self, event):
        """Show context menu on right-click"""
        # Select the item under the cursor
        item = self.semesters_table.identify_row(event.y)
        if item:
            self.semesters_table.selection_set(item)
            self.context_menu.post(event.x_root, event.y_root)
    
    def load_semesters(self):
        """Load semesters data into the table"""
        # Clear existing data
        for item in self.semesters_table.get_children():
            self.semesters_table.delete(item)
        
        conn = get_db_connection()
        cursor = conn.cursor()
        
        # Get semesters with course counts
        cursor.execute("""
            SELECT s.id, s.name, s.start_date, s.end_date, s.is_active,
                   COUNT(DISTINCT pa.course_id) as courses_count
            FROM semesters s
            LEFT JOIN professor_assignments pa ON s.id = pa.semester_id
            GROUP BY s.id
            ORDER BY s.start_date DESC
        """)
        semesters = cursor.fetchall()
        
        close_connection(conn)
        
        # Insert data into table
        for semester in semesters:
            is_active = "Yes" if semester['is_active'] else "No"
            self.semesters_table.insert("", "end", values=(
                semester['id'],
                semester['name'],
                semester['start_date'],
                semester['end_date'],
                is_active,
                semester['courses_count']
            ))
            
            # Highlight active semester
            if semester['is_active']:
                for item in self.semesters_table.get_children():
                    if self.semesters_table.item(item, "values")[0] == semester['id']:
                        self.semesters_table.item(item, tags=("active",))
                        break
        
        # Configure tag for active semester
        self.semesters_table.tag_configure("active", background=ULTheme.ACCENT_COLOR)
    
    def on_semester_double_click(self, event):
        """Handle double-click on semester row"""
        self.on_edit_semester()
    
    def on_add_semester(self):
        """Open add semester form"""
        # This will be handled by the admin dashboard
        from ...controllers.admin_controller import AdminController
        AdminController.show_add_semester_form()
    
    def on_edit_semester(self):
        """Edit selected semester"""
        selected_item = self.semesters_table.selection()
        if not selected_item:
            messagebox.showwarning("Warning", "Please select a semester to edit")
            return

        # Get semester ID
        semester_id = self.semesters_table.item(selected_item[0], "values")[0]

        # Show edit form
        from ...controllers.admin_controller import AdminController
        AdminController.show_edit_semester_form(semester_id)
    
    def on_delete_semester(self):
        """Delete selected semester"""
        selected_item = self.semesters_table.selection()
        if not selected_item:
            messagebox.showwarning("Warning", "Please select a semester to delete")
            return
        
        # Get semester ID and name
        values = self.semesters_table.item(selected_item[0], "values")
        semester_id = values[0]
        semester_name = values[1]
        is_active = values[4] == "Yes"
        courses_count = int(values[5])
        
        # Check if semester is active
        if is_active:
            messagebox.showerror("Error", f"Cannot delete semester '{semester_name}' because it is currently active.")
            return
        
        # Check if semester has courses
        if courses_count > 0:
            messagebox.showerror("Error", f"Cannot delete semester '{semester_name}' because it has {courses_count} courses assigned to it.")
            return
        
        # Confirm deletion
        if messagebox.askyesno("Confirm Delete", f"Are you sure you want to delete semester '{semester_name}'?"):
            # Delete semester (placeholder for now)
            messagebox.showinfo("Delete Semester", f"Delete semester with ID: {semester_id}")
            # In a real implementation, this would call the controller to delete the semester
    
    def on_set_active_semester(self):
        """Set selected semester as active"""
        selected_item = self.semesters_table.selection()
        if not selected_item:
            messagebox.showwarning("Warning", "Please select a semester to set as active")
            return
        
        # Get semester ID and name
        values = self.semesters_table.item(selected_item[0], "values")
        semester_id = values[0]
        semester_name = values[1]
        is_active = values[4] == "Yes"
        
        # Check if semester is already active
        if is_active:
            messagebox.showinfo("Info", f"Semester '{semester_name}' is already active.")
            return
        
        # Confirm setting as active
        if messagebox.askyesno("Confirm Set Active", f"Are you sure you want to set semester '{semester_name}' as active? This will deactivate the currently active semester."):
            # Set semester as active
            success, message = AdminController.set_active_semester(semester_id)
            
            if success:
                messagebox.showinfo("Success", f"Semester '{semester_name}' is now active.")
                self.load_semesters()  # Refresh the list
            else:
                messagebox.showerror("Error", message)
