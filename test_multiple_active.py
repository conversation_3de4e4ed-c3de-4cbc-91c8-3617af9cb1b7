#!/usr/bin/env python3
"""
Test script to verify multiple active semesters functionality
"""

import sys
import os
from pathlib import Path

# Add the current directory to sys.path
current_dir = Path(__file__).resolve().parent
sys.path.append(str(current_dir))

try:
    from university_management_system.database.db_config import get_db_connection, close_connection
    from university_management_system.controllers.admin_controller import <PERSON>minControll<PERSON>
    
    def test_multiple_active_semesters():
        """Test the multiple active semesters functionality"""
        
        print("Testing Multiple Active Semesters Functionality")
        print("=" * 60)
        
        # Test 1: Check current active semesters
        print("\n1. Checking current active semesters...")
        active_semesters = AdminController.get_all_active_semesters()
        print(f"   Found {len(active_semesters)} active semester(s)")
        
        for semester in active_semesters:
            print(f"   - {semester['name']} (ID: {semester['id']})")
        
        # Test 2: Create a test semester with multiple active allowed
        print("\n2. Creating test semester with multiple active allowed...")
        success, message = AdminController.create_semester(
            name="Test Multiple Active Semester",
            start_date="2024-01-01",
            end_date="2024-05-31",
            is_active=True,
            allow_multiple_active=True
        )
        
        if success:
            print(f"   ✓ {message}")
        else:
            print(f"   ✗ {message}")
            return False
        
        # Test 3: Check active semesters again
        print("\n3. Checking active semesters after creation...")
        active_semesters = AdminController.get_all_active_semesters()
        print(f"   Found {len(active_semesters)} active semester(s)")
        
        for semester in active_semesters:
            print(f"   - {semester['name']} (ID: {semester['id']})")
        
        # Test 4: Test set_active_semester with multiple active allowed
        print("\n4. Testing set_active_semester with multiple active...")
        
        # Get all semesters
        conn = get_db_connection()
        cursor = conn.cursor()
        cursor.execute("SELECT * FROM semesters WHERE is_active = 0 LIMIT 1")
        inactive_semester = cursor.fetchone()
        close_connection(conn)
        
        if inactive_semester:
            success, message = AdminController.set_active_semester(
                inactive_semester['id'], 
                allow_multiple_active=True
            )
            
            if success:
                print(f"   ✓ {message}")
                print(f"   ✓ Set '{inactive_semester['name']}' as active (multiple allowed)")
            else:
                print(f"   ✗ {message}")
        else:
            print("   No inactive semesters found to test with")
        
        # Test 5: Final check of active semesters
        print("\n5. Final check of active semesters...")
        active_semesters = AdminController.get_all_active_semesters()
        print(f"   Found {len(active_semesters)} active semester(s)")
        
        for semester in active_semesters:
            print(f"   - {semester['name']} (ID: {semester['id']})")
        
        # Test 6: Test set_active_semester with single active (should deactivate others)
        print("\n6. Testing set_active_semester with single active...")
        
        if len(active_semesters) > 1:
            # Pick the first active semester and set it as the only active one
            first_active = active_semesters[0]
            success, message = AdminController.set_active_semester(
                first_active['id'], 
                allow_multiple_active=False
            )
            
            if success:
                print(f"   ✓ {message}")
                print(f"   ✓ Set '{first_active['name']}' as the only active semester")
            else:
                print(f"   ✗ {message}")
            
            # Check final state
            print("\n7. Final state after single active test...")
            active_semesters = AdminController.get_all_active_semesters()
            print(f"   Found {len(active_semesters)} active semester(s)")
            
            for semester in active_semesters:
                print(f"   - {semester['name']} (ID: {semester['id']})")
        
        print("\n" + "=" * 60)
        print("Multiple Active Semesters Test Completed!")
        return True
    
    if __name__ == "__main__":
        test_multiple_active_semesters()
        
except ImportError as e:
    print(f"Import error: {e}")
    print("Make sure you're running this from the correct directory")
except Exception as e:
    print(f"Error: {e}")
