import re
from datetime import datetime

class Validators:
    @staticmethod
    def validate_email(email):
        """
        Validate email format
        Returns (is_valid, error_message)
        """
        if not email:
            return False, "Email is required"
        
        # Simple email validation regex
        email_regex = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
        if not re.match(email_regex, email):
            return False, "Invalid email format"
        
        return True, ""
    
    @staticmethod
    def validate_phone(phone):
        """
        Validate phone number format
        Returns (is_valid, error_message)
        """
        if not phone:
            return True, ""  # Phone is optional
        
        # Simple phone validation regex (allows various formats)
        phone_regex = r'^[0-9+\-() ]{7,20}$'
        if not re.match(phone_regex, phone):
            return False, "Invalid phone number format"
        
        return True, ""
    
    @staticmethod
    def validate_date(date_str, date_format="%Y-%m-%d"):
        """
        Validate date format
        Returns (is_valid, error_message)
        """
        if not date_str:
            return False, "Date is required"
        
        try:
            datetime.strptime(date_str, date_format)
            return True, ""
        except ValueError:
            return False, f"Invalid date format. Expected format: {date_format}"
    
    @staticmethod
    def validate_required(value, field_name):
        """
        Validate required field
        Returns (is_valid, error_message)
        """
        if not value:
            return False, f"{field_name} is required"
        
        return True, ""
    
    @staticmethod
    def validate_numeric(value, field_name, min_value=None, max_value=None):
        """
        Validate numeric field
        Returns (is_valid, error_message)
        """
        if not value and value != 0:
            return False, f"{field_name} is required"
        
        try:
            num_value = float(value)
            
            if min_value is not None and num_value < min_value:
                return False, f"{field_name} must be at least {min_value}"
            
            if max_value is not None and num_value > max_value:
                return False, f"{field_name} must be at most {max_value}"
            
            return True, ""
        except ValueError:
            return False, f"{field_name} must be a number"
    
    @staticmethod
    def validate_integer(value, field_name, min_value=None, max_value=None):
        """
        Validate integer field
        Returns (is_valid, error_message)
        """
        if not value and value != 0:
            return False, f"{field_name} is required"
        
        try:
            int_value = int(value)
            
            if min_value is not None and int_value < min_value:
                return False, f"{field_name} must be at least {min_value}"
            
            if max_value is not None and int_value > max_value:
                return False, f"{field_name} must be at most {max_value}"
            
            return True, ""
        except ValueError:
            return False, f"{field_name} must be an integer"
    
    @staticmethod
    def validate_length(value, field_name, min_length=None, max_length=None):
        """
        Validate string length
        Returns (is_valid, error_message)
        """
        if value is None:
            value = ""
        
        if min_length is not None and len(value) < min_length:
            return False, f"{field_name} must be at least {min_length} characters"
        
        if max_length is not None and len(value) > max_length:
            return False, f"{field_name} must be at most {max_length} characters"
        
        return True, ""
    
    @staticmethod
    def validate_password(password, confirm_password=None):
        """
        Validate password strength and match with confirm password
        Returns (is_valid, error_message)
        """
        if not password:
            return False, "Password is required"

        if len(password) < 6:
            return False, f"❌ Password is too short! Must be at least 6 characters (currently {len(password)} characters)"

        if confirm_password is not None and password != confirm_password:
            return False, "❌ Passwords do not match! Please ensure both password fields are identical"

        return True, ""
    
    @staticmethod
    def validate_username(username):
        """
        Validate username format
        Returns (is_valid, error_message)
        """
        if not username:
            return False, "Username is required"
        
        if len(username) < 3:
            return False, "Username must be at least 3 characters"
        
        if not re.match(r'^[a-zA-Z0-9_]+$', username):
            return False, "Username can only contain letters, numbers, and underscores"
        
        return True, ""
    
    @staticmethod
    def validate_name(name, field_name="Name"):
        """
        Validate name format
        Returns (is_valid, error_message)
        """
        if not name:
            return False, f"{field_name} is required"
        
        if len(name) < 2:
            return False, f"{field_name} must be at least 2 characters"
        
        if not re.match(r'^[a-zA-Z\s\'-]+$', name):
            return False, f"{field_name} can only contain letters, spaces, hyphens, and apostrophes"
        
        return True, ""
    
    @staticmethod
    def validate_id_format(id_value, pattern, field_name="ID"):
        """
        Validate ID format using regex pattern
        Returns (is_valid, error_message)
        """
        if not id_value:
            return False, f"{field_name} is required"
        
        if not re.match(pattern, id_value):
            return False, f"Invalid {field_name} format"
        
        return True, ""
