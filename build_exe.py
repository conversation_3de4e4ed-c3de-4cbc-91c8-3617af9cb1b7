import os
import subprocess
import sys
import shutil
from pathlib import Path

def check_python_version():
    """Check if Python version is compatible"""
    print("Checking Python version...")
    if sys.version_info < (3, 8):
        print("❌ Python 3.8 or higher is required!")
        return False
    print(f"✅ Python {sys.version_info.major}.{sys.version_info.minor}.{sys.version_info.micro} detected")
    return True

def install_requirements():
    """Install project requirements"""
    print("\n📦 Installing requirements...")
    try:
        subprocess.check_call([sys.executable, "-m", "pip", "install", "-r", "requirements.txt"])
        print("✅ Requirements installed successfully.")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ Failed to install requirements: {e}")
        return False

def install_pyinstaller():
    """Install PyInstaller if not already installed"""
    print("\n🔧 Installing PyInstaller...")
    try:
        subprocess.check_call([sys.executable, "-m", "pip", "install", "pyinstaller>=5.0.0"])
        print("✅ PyInstaller installed successfully.")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ Failed to install PyInstaller: {e}")
        return False

def check_required_files():
    """Check if all required files exist"""
    print("\n📋 Checking required files...")

    required_files = [
        "run.py",
        "requirements.txt",
        "university_management_system/main.py",
        "university_management_system/__init__.py"
    ]

    optional_files = [
        "university_logo.ico",
        "university_logo.png",
        "university_logo_header.png",
        "university_logo_splash.png",
        "university_logo_transparent.png",
        "university_logo_white_bg.png"
    ]

    missing_required = []
    missing_optional = []

    for file in required_files:
        if not os.path.exists(file):
            missing_required.append(file)
        else:
            print(f"✅ Found: {file}")

    for file in optional_files:
        if not os.path.exists(file):
            missing_optional.append(file)
        else:
            print(f"✅ Found: {file}")

    if missing_required:
        print(f"❌ Missing required files: {missing_required}")
        return False

    if missing_optional:
        print(f"⚠️  Missing optional files: {missing_optional}")
        print("   The executable will still work, but may not have all icons/logos")

    return True

def clean_build_directories():
    """Clean previous build directories"""
    print("\n🧹 Cleaning previous build directories...")

    dirs_to_clean = ["build", "dist", "__pycache__"]

    for dir_name in dirs_to_clean:
        if os.path.exists(dir_name):
            try:
                shutil.rmtree(dir_name)
                print(f"✅ Cleaned: {dir_name}")
            except Exception as e:
                print(f"⚠️  Could not clean {dir_name}: {e}")

def build_executable():
    """Build the executable using PyInstaller"""
    print("\n🚀 Building executable...")

    # Base command
    command = [
        sys.executable,
        "-m",
        "PyInstaller",
        "--onefile",
        "--windowed",
        "--name=University_Management_System",
        "--distpath=dist",
        "--workpath=build",
        "--specpath=.",
    ]

    # Add icon if available
    if os.path.exists("university_logo.ico"):
        command.extend(["--icon=university_logo.ico"])
        print("✅ Using university logo as application icon")

    # Add data files
    data_files = [
        ("university_logo.ico", "."),
        ("university_logo.png", "."),
        ("university_logo_header.png", "."),
        ("university_logo_splash.png", "."),
        ("university_logo_transparent.png", "."),
        ("university_logo_white_bg.png", "."),
    ]

    for src, dst in data_files:
        if os.path.exists(src):
            command.extend(["--add-data", f"{src};{dst}"])
            print(f"✅ Including: {src}")

    # Add the entire university_management_system directory
    if os.path.exists("university_management_system"):
        command.extend(["--add-data", "university_management_system;university_management_system"])
        print("✅ Including: university_management_system directory")

    # Add hidden imports for common issues
    hidden_imports = [
        "tkinter",
        "tkinter.ttk",
        "PIL",
        "PIL.Image",
        "PIL.ImageTk",
        "reportlab",
        "reportlab.pdfgen",
        "reportlab.lib",
        "ttkbootstrap",
        "tkcalendar",
        "sqlite3"
    ]

    for module in hidden_imports:
        command.extend(["--hidden-import", module])

    # Add the main script
    command.append("run.py")

    print(f"🔧 PyInstaller command: {' '.join(command)}")

    try:
        subprocess.check_call(command)
        print("✅ Executable built successfully!")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ Failed to build executable: {e}")
        return False

def create_distribution_package():
    """Create a distribution package with executable and database"""
    print("\n📦 Creating distribution package...")

    if not os.path.exists("dist/University_Management_System.exe"):
        print("❌ Executable not found! Build failed.")
        return False

    # Create distribution directory
    dist_dir = "dist/University_Management_System_Package"
    if os.path.exists(dist_dir):
        shutil.rmtree(dist_dir)
    os.makedirs(dist_dir)

    # Copy executable
    shutil.copy2("dist/University_Management_System.exe", dist_dir)
    print("✅ Copied executable")

    # Copy database if it exists
    db_path = "university_management_system/university.db"
    if os.path.exists(db_path):
        shutil.copy2(db_path, dist_dir)
        print("✅ Copied database")

    # Copy logo files
    logo_files = [
        "university_logo.ico",
        "university_logo.png",
        "university_logo_header.png",
        "university_logo_splash.png",
        "university_logo_transparent.png",
        "university_logo_white_bg.png"
    ]

    for logo in logo_files:
        if os.path.exists(logo):
            shutil.copy2(logo, dist_dir)
            print(f"✅ Copied: {logo}")

    # Create README file
    readme_content = """# University Management System

## Installation Instructions

1. Run University_Management_System.exe to start the application
2. The application will create a database automatically on first run
3. Use the following default credentials to login:

### Admin Login:
- Username: admin
- Password: admin123

### Professor Login:
- Username: faisalhafeez
- Password: faisalhafeez

### Student Login:
- Username: john
- Password: john

## System Requirements

- Windows 7 or later
- No additional software required (standalone executable)

## Features

- Admin Panel: Manage students, professors, courses, and semesters
- Professor Portal: Take attendance, manage grades, view courses
- Student Portal: View results, attendance, and generate reports
- PDF Report Generation
- Multi-semester support

## Support

For technical support, please contact the system administrator.

## Version

University Management System v1.0
Built with Python and Tkinter
"""

    with open(os.path.join(dist_dir, "README.txt"), "w") as f:
        f.write(readme_content)
    print("✅ Created README.txt")

    print(f"✅ Distribution package created in: {dist_dir}")
    return True

def main():
    """Main build process"""
    print("🏫 University Management System - Executable Builder")
    print("=" * 60)

    # Check Python version
    if not check_python_version():
        input("Press Enter to exit...")
        return

    # Check required files
    if not check_required_files():
        input("Press Enter to exit...")
        return

    # Clean previous builds
    clean_build_directories()

    # Install requirements
    if not install_requirements():
        input("Press Enter to exit...")
        return

    # Install PyInstaller
    if not install_pyinstaller():
        input("Press Enter to exit...")
        return

    # Build executable
    if not build_executable():
        input("Press Enter to exit...")
        return

    # Create distribution package
    if not create_distribution_package():
        input("Press Enter to exit...")
        return

    print("\n" + "=" * 60)
    print("🎉 BUILD COMPLETED SUCCESSFULLY!")
    print("=" * 60)
    print("📁 Your executable is located in:")
    print("   dist/University_Management_System.exe")
    print("📦 Complete package is located in:")
    print("   dist/University_Management_System_Package/")
    print("\n💡 You can distribute the entire package folder to users")
    print("   or just the .exe file for standalone use.")

    # Show file sizes
    exe_path = "dist/University_Management_System.exe"
    if os.path.exists(exe_path):
        size_mb = os.path.getsize(exe_path) / (1024 * 1024)
        print(f"\n📊 Executable size: {size_mb:.1f} MB")

    print("\n✅ Build process completed successfully!")
    input("\nPress Enter to exit...")

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n\n❌ Build process interrupted by user")
        input("Press Enter to exit...")
    except Exception as e:
        print(f"\n❌ An unexpected error occurred: {e}")
        import traceback
        traceback.print_exc()
        input("Press Enter to exit...")
