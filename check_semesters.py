#!/usr/bin/env python3
"""
Quick script to check the current state of semesters in the database
"""

import sys
from pathlib import Path

# Add the current directory to sys.path
current_dir = Path(__file__).resolve().parent
sys.path.append(str(current_dir))

from university_management_system.database.db_config import get_db_connection, close_connection
from university_management_system.controllers.admin_controller import <PERSON><PERSON>Controll<PERSON>

def display_all_semesters():
    """Display all semesters in database"""
    
    print("\nAll Semesters in Database:")
    print("=" * 80)
    
    try:
        conn = get_db_connection()
        cursor = conn.cursor()
        cursor.execute("SELECT * FROM semesters ORDER BY start_date")
        semesters = cursor.fetchall()
        close_connection(conn)
        
        if semesters:
            print(f"{'ID':<5} {'Name':<30} {'Start Date':<12} {'End Date':<12} {'Active':<8}")
            print("-" * 80)
            for semester in semesters:
                active_status = "Yes" if semester['is_active'] else "No"
                print(f"{semester['id']:<5} {semester['name']:<30} {semester['start_date']:<12} {semester['end_date']:<12} {active_status:<8}")
        else:
            print("No semesters found in database")
            
    except Exception as e:
        print(f"Error retrieving semesters: {e}")

def display_active_semesters():
    """Display only active semesters"""
    
    print("\nActive Semesters:")
    print("=" * 50)
    
    try:
        active_semesters = AdminController.get_all_active_semesters()
        
        if active_semesters:
            print(f"Found {len(active_semesters)} active semester(s):")
            for semester in active_semesters:
                print(f"  - {semester['name']} (ID: {semester['id']})")
        else:
            print("No active semesters found")
            
    except Exception as e:
        print(f"Error retrieving active semesters: {e}")

if __name__ == "__main__":
    print("Checking Semester Status...")
    display_all_semesters()
    display_active_semesters()
