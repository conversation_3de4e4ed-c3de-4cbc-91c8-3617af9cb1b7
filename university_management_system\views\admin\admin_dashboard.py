import tkinter as tk
from tkinter import ttk, messagebox
import os
from pathlib import Path
from PIL import Image, ImageTk
from ...controllers.admin_controller import AdminController
from ...database.db_config import get_db_connection, close_connection
from ...utils.theme import ULTheme, load_and_resize_image
from .add_student_form import AddStudentForm
from .add_department_form import AddDepartmentForm
from .add_semester_form import AddSemesterForm
from .add_course_form import AddCourseForm
from .add_professor_form import AddProfessorForm
from .edit_professor_form import EditProfessorForm
from .professors_list import ProfessorsList
from .departments_list import DepartmentsList
from .edit_department_form import EditDepartmentForm
from .students_list import StudentsList
from .courses_list import CoursesList
from .semesters_list import SemestersList
from .assign_professor_form import AssignProfessorForm
from .professor_assignments_view import ProfessorAssignmentsView

class AdminDashboard:
    def __init__(self, root, user, profile, on_logout):
        self.root = root
        self.user = user
        self.profile = profile
        self.on_logout = on_logout

        # Set reference in AdminController
        AdminController.set_admin_dashboard(self)

        # Apply theme
        self.style = ULTheme.apply_theme(root)

        # Set window title and size
        self.root.title("Admin Dashboard - University of Layyah")
        self.root.geometry("1200x800")
        self.root.protocol("WM_DELETE_WINDOW", self.on_close)

        # Set window to fullscreen mode
        self.root.state('zoomed')

        # Set application icon if available
        BASE_DIR = Path(__file__).resolve().parent.parent.parent
        icon_path = os.path.join(BASE_DIR, 'assets', 'images', 'icon.ico')
        if os.path.exists(icon_path):
            self.root.iconbitmap(icon_path)

        # Create main container
        self.main_container = tk.Frame(self.root, bg=ULTheme.BACKGROUND_COLOR)
        self.main_container.pack(fill=tk.BOTH, expand=True)

        # Create header
        self.header_frame = tk.Frame(self.main_container, bg=ULTheme.PRIMARY_COLOR, height=60)
        self.header_frame.pack(side=tk.TOP, fill=tk.X)
        self.header_frame.pack_propagate(False)  # Prevent shrinking

        # Create body container (will contain sidebar and content)
        self.body_container = tk.Frame(self.main_container, bg=ULTheme.BACKGROUND_COLOR)
        self.body_container.pack(side=tk.BOTTOM, fill=tk.BOTH, expand=True)

        # Create navigation bar at the top of the body
        self.nav_frame = tk.Frame(self.body_container, bg=ULTheme.DARK_COLOR, height=50)
        self.nav_frame.pack(side=tk.TOP, fill=tk.X)
        self.nav_frame.pack_propagate(False)

        # Create content area
        self.content_container = tk.Frame(self.body_container, bg=ULTheme.BACKGROUND_COLOR)
        self.content_container.pack(side=tk.TOP, fill=tk.BOTH, expand=True)

        # Create actual content frame with padding
        self.content_frame = tk.Frame(self.content_container, bg=ULTheme.BACKGROUND_COLOR, padx=20, pady=20)
        self.content_frame.pack(fill=tk.BOTH, expand=True)

        # Setup header content
        self.setup_header()

        # Setup navigation bar
        self.setup_navigation()

        # Show welcome screen by default
        self.show_welcome()

    def _on_mousewheel(self, event):
        """Handle mouse wheel scrolling in sidebar"""
        # Check if mouse is over the sidebar
        x, y = self.sidebar_canvas.winfo_pointerxy()
        widget_under_mouse = self.sidebar_canvas.winfo_containing(x, y)
        if widget_under_mouse and (widget_under_mouse == self.sidebar_canvas or
                                  widget_under_mouse.master == self.sidebar_frame or
                                  widget_under_mouse.master.master == self.sidebar_frame):
            # Scroll 2 units for every mouse wheel click
            self.sidebar_canvas.yview_scroll(int(-1*(event.delta/120)), "units")

    def load_and_display_logo(self, container, *logo_paths):
        """Try to load and display logo from the given paths in order"""
        for logo_path in logo_paths:
            if os.path.exists(logo_path):
                try:
                    # Load and resize logo
                    logo_img = Image.open(logo_path)
                    logo_img = logo_img.resize((40, 40), Image.LANCZOS)
                    logo_photo = ImageTk.PhotoImage(logo_img)

                    # Display logo
                    logo_label = tk.Label(container, image=logo_photo, bg=ULTheme.PRIMARY_COLOR)
                    logo_label.image = logo_photo  # Keep a reference to prevent garbage collection
                    logo_label.pack(side=tk.LEFT, padx=(0, 10))
                    return True
                except Exception as e:
                    print(f"Error loading logo from {logo_path}: {e}")
                    continue

        # If all logo loading attempts fail
        print("No logo found")
        return False



    def setup_header(self):
        """Setup the header with logo, title, and user info"""
        # Left side - Logo and title
        logo_container = tk.Frame(self.header_frame, bg=ULTheme.PRIMARY_COLOR)
        logo_container.pack(side=tk.LEFT, padx=20)

        # Try to load logo
        BASE_DIR = Path(__file__).resolve().parent.parent.parent

        # Define logo paths with priority order
        white_bg_logo_path = os.path.join(BASE_DIR.parent, 'university_logo_white_bg.png')
        header_logo_path = os.path.join(BASE_DIR.parent, 'university_logo_header.png')
        original_logo_path = os.path.join(BASE_DIR.parent, 'university_logo.png')
        default_logo_path = os.path.join(BASE_DIR, 'assets', 'images', 'logo.png')

        # Try to load logos in priority order
        self.load_and_display_logo(logo_container, white_bg_logo_path, header_logo_path, original_logo_path, default_logo_path)

        # University name
        title_label = tk.Label(logo_container, text="University of Layyah",
                              font=(ULTheme.FONT_FAMILY, ULTheme.FONT_SIZE_LARGE, "bold"),
                              fg=ULTheme.LIGHT_COLOR, bg=ULTheme.PRIMARY_COLOR)
        title_label.pack(side=tk.LEFT)

        # Right side - User info and logout
        user_container = tk.Frame(self.header_frame, bg=ULTheme.PRIMARY_COLOR)
        user_container.pack(side=tk.RIGHT, padx=20)

        # User info with icon
        user_info = tk.Frame(user_container, bg=ULTheme.PRIMARY_COLOR)
        user_info.pack(side=tk.LEFT, padx=(0, 15))

        welcome_label = tk.Label(user_info, text=f"Welcome, {self.profile.full_name}",
                                font=(ULTheme.FONT_FAMILY, ULTheme.FONT_SIZE_NORMAL),
                                fg=ULTheme.LIGHT_COLOR, bg=ULTheme.PRIMARY_COLOR)
        welcome_label.pack(anchor=tk.E)

        role_label = tk.Label(user_info, text=f"Role: Admin",
                             font=(ULTheme.FONT_FAMILY, ULTheme.FONT_SIZE_SMALL),
                             fg=ULTheme.LIGHT_COLOR, bg=ULTheme.PRIMARY_COLOR)
        role_label.pack(anchor=tk.E)

        # Logout button
        logout_button = ttk.Button(user_container, text="Logout", style="Secondary.TButton",
                                  command=self.on_logout)
        logout_button.pack(side=tk.RIGHT, pady=10)

    def setup_navigation(self):
        """Setup the navigation bar with menu buttons"""
        menu_sections = [
            {
                "title": "Main",
                "items": [
                    {"text": "Dashboard", "command": self.show_welcome, "icon": "🏠"}
                ]
            },
            {
                "title": "Students",
                "items": [
                    {"text": "View Students", "command": self.show_students, "icon": "👨‍🎓"},
                    {"text": "Add Student", "command": self.show_add_student, "icon": "➕"}
                ]
            },
            {
                "title": "Professors",
                "items": [
                    {"text": "View Professors", "command": self.show_professors, "icon": "👨‍🏫"},
                    {"text": "Add Professor", "command": self.show_add_professor, "icon": "➕"}
                ]
            },
            {
                "title": "Courses",
                "items": [
                    {"text": "View Courses", "command": self.show_courses, "icon": "📚"},
                    {"text": "Add Course", "command": self.show_add_course, "icon": "➕"}
                ]
            },
            {
                "title": "Departments",
                "items": [
                    {"text": "View Departments", "command": self.show_departments, "icon": "🏢"},
                    {"text": "Add Department", "command": self.show_add_department, "icon": "➕"}
                ]
            },
            {
                "title": "Semesters",
                "items": [
                    {"text": "View Semesters", "command": self.show_semesters, "icon": "📅"},
                    {"text": "Add Semester", "command": self.show_add_semester, "icon": "➕"}
                ]
            },
            {
                "title": "Assignments",
                "items": [
                    {"text": "Assign Professor", "command": self.show_assign_professor, "icon": "🔗"},
                    {"text": "Enroll Student", "command": self.show_enroll_student, "icon": "📝"}
                ]
            },
            {
                "title": "Reports",
                "items": [
                    {"text": "Attendance Reports", "command": self.show_attendance_reports, "icon": "📋"},
                    {"text": "Result Reports", "command": self.show_result_reports, "icon": "📊"}
                ]
            }
        ]

        # Create navigation buttons
        for section in menu_sections:
            for item in section["items"]:
                if item["text"] == "Dashboard":
                    btn = tk.Button(self.nav_frame, text=f"{item['icon']} {item['text']}", command=item["command"],
                                    bg="#2196F3", fg="white", activebackground="#1976D2", activeforeground="white",
                                    font=("Arial", 11, "bold"), relief="flat", padx=15, pady=5, cursor="hand2")
                else:
                    btn = tk.Button(self.nav_frame, text=f"{item['icon']} {item['text']}", command=item["command"],
                                    bg="#FFD700", fg="white", activebackground="#FFA500", activeforeground="white",
                                    font=("Arial", 11, "bold"), relief="flat", padx=15, pady=5, cursor="hand2")
                btn.pack(side=tk.LEFT, padx=5, pady=8)
                def on_enter(e, b=btn):
                    if item["text"] == "Dashboard":
                        b.config(bg="#1976D2")
                    else:
                        b.config(bg="#FFA500")
                def on_leave(e, b=btn):
                    if item["text"] == "Dashboard":
                        b.config(bg="#2196F3")
                    else:
                        b.config(bg="#FFD700")
                btn.bind("<Enter>", on_enter)
                btn.bind("<Leave>", on_leave)


    def clear_content(self):
        """Clear the content frame"""
        for widget in self.content_frame.winfo_children():
            widget.destroy()

    def show_welcome(self):
        """Show welcome screen with dashboard summary"""
        self.clear_content()

        # Create welcome container
        welcome_container = tk.Frame(self.content_frame, bg=ULTheme.BACKGROUND_COLOR)
        welcome_container.pack(fill=tk.BOTH, expand=True)

        # Header section
        header_container = tk.Frame(welcome_container, bg=ULTheme.BACKGROUND_COLOR, padx=10, pady=10)
        header_container.pack(fill=tk.X)

        # Welcome message
        welcome_label = tk.Label(header_container,
                               text=f"Welcome, {self.profile.first_name}!",
                               font=(ULTheme.FONT_FAMILY, ULTheme.FONT_SIZE_XXLARGE, "bold"),
                               fg=ULTheme.DARK_COLOR, bg=ULTheme.BACKGROUND_COLOR)
        welcome_label.pack(side=tk.LEFT)

        # Current date (could be dynamic in a real app)
        date_label = tk.Label(header_container,
                             text="Today: June 15, 2023",
                             font=(ULTheme.FONT_FAMILY, ULTheme.FONT_SIZE_NORMAL),
                             fg=ULTheme.TEXT_MUTED, bg=ULTheme.BACKGROUND_COLOR)
        date_label.pack(side=tk.RIGHT, padx=10, pady=10)

        # Subtitle
        subtitle_label = tk.Label(welcome_container,
                                text="University Management Dashboard",
                                font=(ULTheme.FONT_FAMILY, ULTheme.FONT_SIZE_LARGE),
                                fg=ULTheme.TEXT_MUTED, bg=ULTheme.BACKGROUND_COLOR)
        subtitle_label.pack(anchor=tk.W, padx=10, pady=(0, 20))

        # Get summary data
        conn = get_db_connection()
        cursor = conn.cursor()

        # Get counts
        cursor.execute("SELECT COUNT(*) FROM students")
        student_count = cursor.fetchone()[0]

        cursor.execute("SELECT COUNT(*) FROM professors")
        professor_count = cursor.fetchone()[0]

        cursor.execute("SELECT COUNT(*) FROM courses")
        course_count = cursor.fetchone()[0]

        cursor.execute("SELECT COUNT(*) FROM departments")
        department_count = cursor.fetchone()[0]

        cursor.execute("SELECT COUNT(*) FROM semesters")
        semester_count = cursor.fetchone()[0]

        cursor.execute("SELECT name FROM semesters WHERE is_active = 1")
        active_semester = cursor.fetchone()
        active_semester_name = active_semester[0] if active_semester else "None"

        close_connection(conn)

        # Create summary cards container
        cards_container = tk.Frame(welcome_container, bg=ULTheme.BACKGROUND_COLOR)
        cards_container.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

        # Configure grid
        for i in range(3):
            cards_container.columnconfigure(i, weight=1)
        for i in range(2):
            cards_container.rowconfigure(i, weight=1)

        # Card data with icons
        cards_data = [
            {"title": "Students", "count": student_count, "icon": "👨‍🎓", "command": self.show_students, "color": "#4CAF50"},
            {"title": "Professors", "count": professor_count, "icon": "👨‍🏫", "command": self.show_professors, "color": "#2196F3"},
            {"title": "Courses", "count": course_count, "icon": "📚", "command": self.show_courses, "color": "#FF9800"},
            {"title": "Departments", "count": department_count, "icon": "🏢", "command": self.show_departments, "color": "#9C27B0"},
            {"title": "Semesters", "count": semester_count, "icon": "📅", "command": self.show_semesters, "color": "#F44336"},
            {"title": "Active Semester", "count": active_semester_name, "icon": "✓", "command": self.show_semesters, "color": "#009688", "is_text": True}
        ]

        # Create cards
        for i, card_data in enumerate(cards_data):
            row = i // 3
            col = i % 3

            # Card frame
            card = tk.Frame(cards_container, bg=ULTheme.LIGHT_COLOR, padx=15, pady=15,
                           highlightbackground=card_data["color"], highlightthickness=1)
            card.grid(row=row, column=col, padx=10, pady=10, sticky="nsew")

            # Top section with icon and title
            top_frame = tk.Frame(card, bg=ULTheme.LIGHT_COLOR)
            top_frame.pack(fill=tk.X)

            # Icon
            icon_label = tk.Label(top_frame, text=card_data["icon"],
                                 font=(ULTheme.FONT_FAMILY, 24),
                                 fg=card_data["color"], bg=ULTheme.LIGHT_COLOR)
            icon_label.pack(side=tk.LEFT)

            # Title
            title_label = tk.Label(top_frame, text=card_data["title"],
                                  font=(ULTheme.FONT_FAMILY, ULTheme.FONT_SIZE_LARGE, "bold"),
                                  fg=ULTheme.DARK_COLOR, bg=ULTheme.LIGHT_COLOR)
            title_label.pack(side=tk.RIGHT)

            # Count/Value
            if card_data.get("is_text", False):
                count_label = tk.Label(card, text=card_data["count"],
                                      font=(ULTheme.FONT_FAMILY, ULTheme.FONT_SIZE_XLARGE),
                                      fg=ULTheme.DARK_COLOR, bg=ULTheme.LIGHT_COLOR)
            else:
                count_label = tk.Label(card, text=str(card_data["count"]),
                                      font=(ULTheme.FONT_FAMILY, 36, "bold"),
                                      fg=ULTheme.DARK_COLOR, bg=ULTheme.LIGHT_COLOR)
            count_label.pack(pady=(15, 15))

            # Button
            button_text = f"View {card_data['title']}" if card_data["title"] != "Active Semester" else "Manage Semesters"
            view_button = ttk.Button(card, text=button_text, command=card_data["command"])
            view_button.pack(fill=tk.X)

            # Make the whole card clickable
            for widget in [card, icon_label, title_label, count_label]:
                widget.bind("<Button-1>", lambda e, cmd=card_data["command"]: cmd())

                # Minimal hover effect (reduced as per user request)
                widget.bind("<Enter>", lambda e, c=card, color=card_data["color"]:
                           c.config(highlightbackground=color, highlightthickness=1, cursor="hand2"))
                widget.bind("<Leave>", lambda e, c=card, color=card_data["color"]:
                           c.config(highlightbackground=color, highlightthickness=1))

    def show_students(self):
        """Show students list"""
        self.clear_content()
        # Create and show students list
        StudentsList(self.content_frame)

    def show_add_student(self):
        """Show add student form"""
        self.clear_content()
        # Create and show add student form
        AddStudentForm(self.content_frame)

    def show_professors(self):
        """Show professors list"""
        self.clear_content()
        # Create and show professors list
        ProfessorsList(self.content_frame)

    def show_add_professor(self):
        """Show add professor form"""
        self.clear_content()
        # Create and show add professor form
        AddProfessorForm(self.content_frame)

    def show_edit_professor(self, professor_id):
        """Show edit professor form"""
        self.clear_content()
        # Create and show edit professor form
        EditProfessorForm(self.content_frame, professor_id)

    def show_professor_assignments(self, professor_id):
        """Show professor assignments view"""
        self.clear_content()
        # Create and show professor assignments view
        ProfessorAssignmentsView(self.content_frame, professor_id)

    def show_courses(self):
        """Show courses list"""
        self.clear_content()
        # Create and show courses list
        CoursesList(self.content_frame)

    def show_add_course(self):
        """Show add course form"""
        self.clear_content()
        # Create and show add course form
        AddCourseForm(self.content_frame)

    def show_departments(self):
        """Show departments list"""
        self.clear_content()
        # Create and show departments list
        DepartmentsList(self.content_frame)

    def show_add_department(self):
        """Show add department form"""
        self.clear_content()
        # Create and show add department form
        AddDepartmentForm(self.content_frame)

    def show_edit_department(self, department_id):
        """Show edit department form"""
        self.clear_content()
        # Create and show edit department form
        EditDepartmentForm(self.content_frame, department_id)

    def show_edit_semester(self, semester_id):
        """Show edit semester form"""
        self.clear_content()
        # Create and show edit semester form
        from .edit_semester_form import EditSemesterForm
        EditSemesterForm(self.content_frame, semester_id)

    def show_semesters(self):
        """Show semesters list"""
        self.clear_content()
        # Create and show semesters list
        SemestersList(self.content_frame)

    def show_add_semester(self):
        """Show add semester form"""
        self.clear_content()
        # Create and show add semester form
        AddSemesterForm(self.content_frame)

    def show_assign_professor(self, professor_id=None):
        """Show assign professor form"""
        self.clear_content()
        # Create and show assign professor form
        from .assign_professor_form import AssignProfessorForm
        AssignProfessorForm(self.content_frame, professor_id)

    def show_enroll_student(self, student_id=None):
        """Show enroll student form"""
        self.clear_content()
        # Create and show enroll student form
        from .enroll_student_form import EnrollStudentForm
        EnrollStudentForm(self.content_frame, student_id)

    def show_attendance_reports(self):
        """Show attendance reports"""
        self.clear_content()
        # Placeholder for attendance reports
        ttk.Label(self.content_frame, text="Attendance Reports",
                 font=(ULTheme.FONT_FAMILY, ULTheme.FONT_SIZE_XLARGE, "bold")).pack(pady=20)
        ttk.Label(self.content_frame, text="This feature will be implemented in the next version.").pack()

    def show_result_reports(self):
        """Show result reports"""
        self.clear_content()
        # Placeholder for result reports
        ttk.Label(self.content_frame, text="Result Reports",
                 font=(ULTheme.FONT_FAMILY, ULTheme.FONT_SIZE_XLARGE, "bold")).pack(pady=20)
        ttk.Label(self.content_frame, text="This feature will be implemented in the next version.").pack()

    def on_close(self):
        """Handle window close event"""
        if messagebox.askokcancel("Quit", "Do you want to quit?"):
            self.root.destroy()
            self.on_logout()
