import tkinter as tk
from tkinter import ttk, messagebox
from ...controllers.admin_controller import AdminController
from ...database.db_config import get_db_connection, close_connection
from ...utils.theme import ULTheme

class StudentsList:
    def __init__(self, parent):
        self.parent = parent

        # Create main frame
        self.main_frame = ttk.Frame(parent, padding=20)
        self.main_frame.pack(fill=tk.BOTH, expand=True)

        # Create title and action buttons
        self.create_header()

        # Create search frame
        self.create_search_frame()

        # Create students table
        self.create_students_table()

        # Load students data
        self.load_students()

    def create_header(self):
        """Create header with title and action buttons"""
        # Header frame
        header_frame = ttk.Frame(self.main_frame)
        header_frame.pack(fill=tk.X, pady=(0, 20))

        # Title
        title_label = ttk.Label(header_frame, text="Students List",
                               font=(ULTheme.FONT_FAMILY, ULTheme.FONT_SIZE_XLARGE, "bold"))
        title_label.pack(side=tk.LEFT)

        # Action buttons frame
        action_buttons_frame = ttk.Frame(header_frame)
        action_buttons_frame.pack(side=tk.RIGHT)

        # Add student button
        add_button = ttk.Button(action_buttons_frame, text="Add Student",
                               command=self.on_add_student)
        add_button.pack(side=tk.LEFT, padx=5)

        # Refresh button
        refresh_button = ttk.Button(action_buttons_frame, text="Refresh",
                                   command=self.load_students)
        refresh_button.pack(side=tk.LEFT, padx=5)

    def create_search_frame(self):
        """Create search frame with search field and filters"""
        # Search frame
        search_frame = ttk.Frame(self.main_frame)
        search_frame.pack(fill=tk.X, pady=(0, 20))

        # Search label
        search_label = ttk.Label(search_frame, text="Search:")
        search_label.pack(side=tk.LEFT, padx=(0, 5))

        # Search entry
        self.search_var = tk.StringVar()
        self.search_entry = ttk.Entry(search_frame, width=30, textvariable=self.search_var)
        self.search_entry.pack(side=tk.LEFT, padx=(0, 10))

        # Search button
        search_button = ttk.Button(search_frame, text="Search", command=self.search_students)
        search_button.pack(side=tk.LEFT, padx=5)

        # Clear search button
        clear_button = ttk.Button(search_frame, text="Clear", command=self.clear_search)
        clear_button.pack(side=tk.LEFT, padx=5)

        # Department filter
        department_label = ttk.Label(search_frame, text="Department:")
        department_label.pack(side=tk.LEFT, padx=(20, 5))

        self.department_var = tk.StringVar()
        self.department_combobox = ttk.Combobox(search_frame, width=20,
                                              textvariable=self.department_var, state="readonly")
        self.department_combobox.pack(side=tk.LEFT, padx=(0, 10))

        # Load departments for filter
        self.load_departments()

        # Department filter button
        filter_button = ttk.Button(search_frame, text="Filter", command=self.filter_students)
        filter_button.pack(side=tk.LEFT, padx=5)

    def create_students_table(self):
        """Create students table with scrollbar"""
        # Table frame with scrollbar
        table_frame = ttk.Frame(self.main_frame)
        table_frame.pack(fill=tk.BOTH, expand=True)

        # Scrollbar
        scrollbar = ttk.Scrollbar(table_frame)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

        # Treeview for students table
        columns = ("id", "student_id", "name", "email", "department", "semester")
        self.students_table = ttk.Treeview(table_frame, columns=columns, show="headings",
                                         yscrollcommand=scrollbar.set)

        # Configure scrollbar
        scrollbar.config(command=self.students_table.yview)

        # Configure column headings
        self.students_table.heading("id", text="ID")
        self.students_table.heading("student_id", text="Student ID")
        self.students_table.heading("name", text="Name")
        self.students_table.heading("email", text="Email")
        self.students_table.heading("department", text="Department")
        self.students_table.heading("semester", text="Semester")

        # Configure column widths
        self.students_table.column("id", width=50, anchor=tk.CENTER)
        self.students_table.column("student_id", width=100, anchor=tk.CENTER)
        self.students_table.column("name", width=200)
        self.students_table.column("email", width=200)
        self.students_table.column("department", width=150)
        self.students_table.column("semester", width=100, anchor=tk.CENTER)

        # Pack table
        self.students_table.pack(fill=tk.BOTH, expand=True)

        # Bind double-click event for editing
        self.students_table.bind("<Double-1>", self.on_student_double_click)

        # Add right-click context menu
        self.create_context_menu()

    def create_context_menu(self):
        """Create right-click context menu for students table"""
        self.context_menu = tk.Menu(self.students_table, tearoff=0)
        self.context_menu.add_command(label="Edit", command=self.on_edit_student)
        self.context_menu.add_command(label="Delete", command=self.on_delete_student)
        self.context_menu.add_separator()
        self.context_menu.add_command(label="Enroll in Course", command=self.on_enroll_student)

        # Bind right-click event
        self.students_table.bind("<Button-3>", self.show_context_menu)

    def show_context_menu(self, event):
        """Show context menu on right-click"""
        # Select the item under the cursor
        item = self.students_table.identify_row(event.y)
        if item:
            self.students_table.selection_set(item)
            self.context_menu.post(event.x_root, event.y_root)

    def load_departments(self):
        """Load departments for filter dropdown"""
        conn = get_db_connection()
        cursor = conn.cursor()

        cursor.execute("SELECT id, name FROM departments ORDER BY name")
        departments = cursor.fetchall()

        close_connection(conn)

        # Add "All Departments" option
        department_values = ["All Departments"]
        self.departments = {"All Departments": None}

        if departments:
            for dept in departments:
                department_values.append(dept['name'])
                self.departments[dept['name']] = dept['id']

        self.department_combobox['values'] = department_values
        self.department_var.set("All Departments")

    def load_students(self):
        """Load students data into the table"""
        # Clear existing data
        for item in self.students_table.get_children():
            self.students_table.delete(item)

        conn = get_db_connection()
        cursor = conn.cursor()

        cursor.execute("""
            SELECT s.id, s.student_id, s.first_name, s.last_name, s.email,
                   s.semester, d.name as department_name
            FROM students s
            JOIN departments d ON s.department_id = d.id
            ORDER BY s.last_name, s.first_name
        """)
        students = cursor.fetchall()

        close_connection(conn)

        # Insert data into table
        for student in students:
            full_name = f"{student['first_name']} {student['last_name']}"
            self.students_table.insert("", "end", values=(
                student['id'],
                student['student_id'],
                full_name,
                student['email'],
                student['department_name'],
                student['semester']
            ))

    def search_students(self):
        """Search students by name, ID, or email"""
        search_term = self.search_var.get().strip().lower()
        if not search_term:
            self.load_students()
            return

        # Clear existing data
        for item in self.students_table.get_children():
            self.students_table.delete(item)

        conn = get_db_connection()
        cursor = conn.cursor()

        cursor.execute("""
            SELECT s.id, s.student_id, s.first_name, s.last_name, s.email,
                   s.semester, d.name as department_name
            FROM students s
            JOIN departments d ON s.department_id = d.id
            WHERE LOWER(s.first_name) LIKE ? OR LOWER(s.last_name) LIKE ? OR
                  LOWER(s.student_id) LIKE ? OR LOWER(s.email) LIKE ?
            ORDER BY s.last_name, s.first_name
        """, (f"%{search_term}%", f"%{search_term}%", f"%{search_term}%", f"%{search_term}%"))
        students = cursor.fetchall()

        close_connection(conn)

        # Insert data into table
        for student in students:
            full_name = f"{student['first_name']} {student['last_name']}"
            self.students_table.insert("", "end", values=(
                student['id'],
                student['student_id'],
                full_name,
                student['email'],
                student['department_name'],
                student['semester']
            ))

    def filter_students(self):
        """Filter students by department"""
        department = self.department_var.get()
        department_id = self.departments.get(department)

        if department == "All Departments" or not department_id:
            self.load_students()
            return

        # Clear existing data
        for item in self.students_table.get_children():
            self.students_table.delete(item)

        conn = get_db_connection()
        cursor = conn.cursor()

        cursor.execute("""
            SELECT s.id, s.student_id, s.first_name, s.last_name, s.email,
                   s.semester, d.name as department_name
            FROM students s
            JOIN departments d ON s.department_id = d.id
            WHERE s.department_id = ?
            ORDER BY s.last_name, s.first_name
        """, (department_id,))
        students = cursor.fetchall()

        close_connection(conn)

        # Insert data into table
        for student in students:
            full_name = f"{student['first_name']} {student['last_name']}"
            self.students_table.insert("", "end", values=(
                student['id'],
                student['student_id'],
                full_name,
                student['email'],
                student['department_name'],
                student['semester']
            ))

    def clear_search(self):
        """Clear search field and reload all students"""
        self.search_var.set("")
        self.department_var.set("All Departments")
        self.load_students()

    def on_student_double_click(self, event):
        """Handle double-click on student row"""
        self.on_edit_student()

    def on_add_student(self):
        """Open add student form"""
        # This will be handled by the admin dashboard
        from ...controllers.admin_controller import AdminController
        AdminController.show_add_student_form()

    def on_edit_student(self):
        """Edit selected student"""
        selected_item = self.students_table.selection()
        if not selected_item:
            messagebox.showwarning("Warning", "Please select a student to edit")
            return

        # Get student ID
        student_id = self.students_table.item(selected_item[0], "values")[0]

        # Show edit form (placeholder for now)
        messagebox.showinfo("Edit Student", f"Edit student with ID: {student_id}")
        # In a real implementation, this would open an edit form

    def on_delete_student(self):
        """Delete selected student"""
        selected_item = self.students_table.selection()
        if not selected_item:
            messagebox.showwarning("Warning", "Please select a student to delete")
            return

        # Get student ID and name
        values = self.students_table.item(selected_item[0], "values")
        student_id = values[0]
        student_name = values[2]

        # Confirm deletion
        if messagebox.askyesno("Confirm Delete",
                              f"Are you sure you want to delete student {student_name}?\n\n"
                              f"This will also delete all related records including:\n"
                              f"• Attendance records\n"
                              f"• Academic results\n"
                              f"• Course enrollments\n"
                              f"• User account\n\n"
                              f"This action cannot be undone!"):
            # Delete student using the controller
            from ...controllers.admin_controller import AdminController
            success, message = AdminController.delete_student(student_id)

            if success:
                messagebox.showinfo("Success", "Student deleted successfully")
                self.load_students()  # Refresh the list
            else:
                messagebox.showerror("Error", message)

    def on_enroll_student(self):
        """Enroll selected student in a course"""
        selected_item = self.students_table.selection()
        if not selected_item:
            messagebox.showwarning("Warning", "Please select a student to enroll")
            return

        # Get student ID
        student_id = self.students_table.item(selected_item[0], "values")[0]

        # Show enroll form
        from ...controllers.admin_controller import AdminController
        AdminController.show_enroll_student_form(student_id)
