@echo off
echo.
echo ========================================
echo  University Management System Builder
echo ========================================
echo.

REM Check if Python is installed
python --version >nul 2>&1
if errorlevel 1 (
    echo ERROR: Python is not installed or not in PATH
    echo Please install Python 3.8 or higher from https://python.org
    pause
    exit /b 1
)

echo Starting build process...
echo.

REM Run the build script
python build_exe.py

echo.
echo Build process completed.
pause
